import createNextIntlPlugin from "next-intl/plugin";
import { withSentryConfig } from "@sentry/nextjs";

// 开发环境简化的安全头部配置
// 生产环境使用 middleware 配置，这里只是为了开发环境
const SECURITY_HEADERS = [
  {
    key: "X-Frame-Options",
    value: "SAMEORIGIN", // 开发环境允许同源嵌入，方便调试
  },
];

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 临时禁用 React Strict Mode 来解决 insertBefore DOM 错误
  // 这个错误通常由 React 18 并发渲染和多个 Portal 组件冲突引起
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        port: "",
        pathname: "/a/**",
      },
    ],
  },
  // 添加静态文件的缓存控制和安全头部
  async headers() {
    return [
      {
        // 为所有页面添加安全头部，防止点击劫持等攻击
        source: "/(.*)",
        headers: SECURITY_HEADERS,
      },
      {
        // 为FFmpeg文件添加缓存控制头
        source: "/ffmpeg/:path*",
        headers: [
          {
            key: "Cache-Control",
            // 设置为一年的缓存时间，并标记为immutable
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/api/:path*", // 前端请求的路径
        destination: "http://localhost:8000/:path*", // 代理到本地后端的路径
      },
    ];
  },
  async redirects() {
    return [
      {
        source: "/dashboard/:transcriptionId",
        destination: "/transcriptions/:transcriptionId",
        permanent: true, // 设置为永久重定向 (301)
      },
    ];
  },
};

// 先应用 nextIntl 插件，再应用 Sentry 配置
export default withSentryConfig(withNextIntl(nextConfig), {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: "uniscribe",
  project: "uniscribe-web",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
