"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

// 这个组件专门模拟实际应用中可能出现的 Radix UI Portal 冲突场景
const RadixUIConflictTest = () => {
  const [dialogs, setDialogs] = useState({
    dialog1: false,
    dialog2: false,
    dialog3: false,
    alert1: false,
    alert2: false,
  });
  const [isRunning, setIsRunning] = useState(false);

  // 模拟实际应用中的复杂状态变化
  const simulateRealWorldScenario = () => {
    setIsRunning(true);
    console.log("🎭 Starting real-world Dialog conflict simulation...");

    let step = 0;
    const scenarios = [
      // 场景1: 快速打开多个 Dialog
      () => {
        console.log("Step 1: Opening multiple dialogs rapidly");
        setDialogs(prev => ({ ...prev, dialog1: true }));
        setTimeout(() => setDialogs(prev => ({ ...prev, dialog2: true })), 10);
        setTimeout(() => setDialogs(prev => ({ ...prev, alert1: true })), 20);
      },
      
      // 场景2: 在 Dialog 打开时快速切换状态
      () => {
        console.log("Step 2: Rapid state switching");
        setDialogs(prev => ({ ...prev, dialog1: false, dialog3: true }));
        setTimeout(() => setDialogs(prev => ({ ...prev, alert2: true })), 5);
        setTimeout(() => setDialogs(prev => ({ ...prev, dialog2: false })), 15);
      },
      
      // 场景3: 同时关闭多个 Dialog
      () => {
        console.log("Step 3: Closing multiple dialogs simultaneously");
        setDialogs(prev => ({ 
          ...prev, 
          dialog3: false, 
          alert1: false, 
          alert2: false 
        }));
      },
      
      // 场景4: 重复打开关闭同一个 Dialog
      () => {
        console.log("Step 4: Rapid open/close same dialog");
        for (let i = 0; i < 5; i++) {
          setTimeout(() => {
            setDialogs(prev => ({ ...prev, dialog1: true }));
            setTimeout(() => setDialogs(prev => ({ ...prev, dialog1: false })), 10);
          }, i * 30);
        }
      },
      
      // 场景5: 模拟用户快速点击
      () => {
        console.log("Step 5: Simulating rapid user clicks");
        const rapidClicks = [
          { dialog: 'dialog1', delay: 0 },
          { dialog: 'dialog2', delay: 5 },
          { dialog: 'dialog1', delay: 10, value: false },
          { dialog: 'alert1', delay: 15 },
          { dialog: 'dialog3', delay: 20 },
          { dialog: 'dialog2', delay: 25, value: false },
        ];
        
        rapidClicks.forEach(({ dialog, delay, value = true }) => {
          setTimeout(() => {
            setDialogs(prev => ({ ...prev, [dialog]: value }));
          }, delay);
        });
      }
    ];

    const runScenario = () => {
      if (step < scenarios.length) {
        scenarios[step]();
        step++;
        setTimeout(runScenario, 200);
      } else {
        // 清理所有状态
        setTimeout(() => {
          setDialogs({
            dialog1: false,
            dialog2: false,
            dialog3: false,
            alert1: false,
            alert2: false,
          });
          setIsRunning(false);
          console.log("🎭 Real-world simulation completed");
        }, 500);
      }
    };

    runScenario();
  };

  // 监听 DOM 变化
  useEffect(() => {
    if (!isRunning) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const addedNodes = Array.from(mutation.addedNodes);
          const removedNodes = Array.from(mutation.removedNodes);
          
          if (addedNodes.length > 0 || removedNodes.length > 0) {
            console.log("🔍 DOM Mutation detected:", {
              added: addedNodes.length,
              removed: removedNodes.length,
              target: mutation.target.tagName,
            });
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [isRunning]);

  return (
    <div className="space-y-4 bg-purple-50 border border-purple-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-purple-800">
        🎭 Real-World Radix UI Conflict Test
      </h3>
      <p className="text-sm text-purple-700">
        This test simulates actual application scenarios where multiple Radix UI components
        might conflict, including rapid state changes and overlapping Portal operations.
      </p>
      
      <div className="flex gap-4 items-center flex-wrap">
        <Button
          onClick={simulateRealWorldScenario}
          disabled={isRunning}
          variant="destructive"
          className="bg-purple-600 hover:bg-purple-700"
        >
          {isRunning ? "Running Simulation..." : "🎭 Start Real-World Test"}
        </Button>
        
        <div className="text-sm text-purple-600">
          Active: {Object.entries(dialogs).filter(([_, isOpen]) => isOpen).map(([name]) => name).join(', ') || 'None'}
        </div>
      </div>

      {isRunning && (
        <div className="bg-purple-100 border border-purple-300 rounded p-3">
          <p className="text-purple-800 text-sm">
            🎭 Simulation running... Watch console for DOM mutations and potential errors!
          </p>
        </div>
      )}

      {/* 多个 Dialog 组件 - 模拟实际应用场景 */}
      <Dialog open={dialogs.dialog1} onOpenChange={(open) => setDialogs(prev => ({ ...prev, dialog1: open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog 1 - User Profile</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>This simulates a user profile dialog that might open in a real app.</p>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={dialogs.dialog2} onOpenChange={(open) => setDialogs(prev => ({ ...prev, dialog2: open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog 2 - Settings</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>This simulates a settings dialog.</p>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={dialogs.dialog3} onOpenChange={(open) => setDialogs(prev => ({ ...prev, dialog3: open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Dialog 3 - Upload File</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>This simulates a file upload dialog.</p>
          </div>
        </DialogContent>
      </Dialog>

      <AlertDialog open={dialogs.alert1} onOpenChange={(open) => setDialogs(prev => ({ ...prev, alert1: open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this item?
            </AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={dialogs.alert2} onOpenChange={(open) => setDialogs(prev => ({ ...prev, alert2: open }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Network Error</AlertDialogTitle>
            <AlertDialogDescription>
              Failed to connect to the server. Please try again.
            </AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default RadixUIConflictTest;
