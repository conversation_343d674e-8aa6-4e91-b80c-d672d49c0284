"use client";

import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";

// 这个组件专门设计来触发 insertBefore 错误
const PortalStressTest = () => {
  const [portals, setPortals] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const intervalRef = useRef(null);
  const portalCountRef = useRef(0);

  // 创建一个会快速挂载和卸载的 Portal 组件，模拟 Radix UI 的行为
  const StressPortal = ({ id, onUnmount }) => {
    const [isUnmounting, setIsUnmounting] = useState(false);

    useEffect(() => {
      // 模拟 React Strict Mode 的双重渲染
      console.log(`Portal ${id} mounting`);

      // 随机延迟后自动卸载
      const timeout = setTimeout(
        () => {
          console.log(`Portal ${id} starting unmount`);
          setIsUnmounting(true);

          // 延迟实际卸载，模拟动画或清理过程
          setTimeout(() => {
            onUnmount(id);
          }, 10);
        },
        Math.random() * 50 + 20
      ); // 20-70ms 随机延迟

      return () => {
        console.log(`Portal ${id} cleanup`);
        clearTimeout(timeout);
      };
    }, [id, onUnmount]);

    // 在卸载过程中仍然渲染，但可能导致 DOM 状态不一致
    return createPortal(
      <div
        style={{
          position: "fixed",
          top: `${Math.random() * 100}px`,
          left: `${Math.random() * 100}px`,
          width: "100px",
          height: "50px",
          backgroundColor: isUnmounting
            ? "rgba(255, 255, 0, 0.5)"
            : "rgba(255, 0, 0, 0.5)",
          border: "1px solid red",
          zIndex: 9999,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontSize: "12px",
          color: "white",
          opacity: isUnmounting ? 0.3 : 1,
        }}
      >
        Portal {id} {isUnmounting ? "(unmounting)" : ""}
      </div>,
      document.body
    );
  };

  const addPortal = () => {
    const id = portalCountRef.current++;
    setPortals((prev) => [...prev, id]);
  };

  const removePortal = (id) => {
    setPortals((prev) => prev.filter((portalId) => portalId !== id));
  };

  const startStressTest = () => {
    setIsRunning(true);
    console.log("🚨 Starting aggressive Portal stress test...");

    // 每 10ms 创建一个新的 Portal
    intervalRef.current = setInterval(() => {
      addPortal();

      // 随机移除一些现有的 Portal
      setPortals((prev) => {
        if (prev.length > 5) {
          const toRemove = Math.floor(Math.random() * 3);
          return prev.slice(toRemove);
        }
        return prev;
      });
    }, 10);

    // 10秒后停止测试
    setTimeout(() => {
      stopStressTest();
    }, 10000);
  };

  const stopStressTest = () => {
    setIsRunning(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    // 清理所有 Portal
    setPortals([]);
    console.log("🛑 Portal stress test stopped");
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className="space-y-4 bg-red-50 border border-red-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-red-800">
        🔥 Aggressive Portal Stress Test
      </h3>
      <p className="text-sm text-red-700">
        This test creates and destroys Portal components every 10ms to maximize
        the chance of DOM conflicts.
        <strong> This is VERY likely to cause insertBefore errors!</strong>
      </p>

      <div className="flex gap-4 items-center">
        <Button
          onClick={startStressTest}
          disabled={isRunning}
          variant="destructive"
          className="bg-red-600 hover:bg-red-700"
        >
          {isRunning ? "Running..." : "🚨 Start Aggressive Test"}
        </Button>

        <Button
          onClick={stopStressTest}
          disabled={!isRunning}
          variant="outline"
        >
          Stop Test
        </Button>

        <span className="text-sm text-red-600">
          Active Portals: {portals.length}
        </span>
      </div>

      {isRunning && (
        <div className="bg-red-100 border border-red-300 rounded p-3">
          <p className="text-red-800 text-sm">
            ⚠️ Aggressive test running! Creating Portal every 10ms...
            <br />
            Watch the console for DOM errors and check the red boxes appearing
            on screen!
          </p>
        </div>
      )}

      {/* 渲染所有 Portal */}
      {portals.map((id) => (
        <StressPortal key={id} id={id} onUnmount={removePortal} />
      ))}
    </div>
  );
};

export default PortalStressTest;
