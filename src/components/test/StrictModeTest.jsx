"use client";

import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";

// 这个组件专门测试 React Strict Mode 可能导致的 DOM 操作问题
const StrictModeTest = () => {
  const [portals, setPortals] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const renderCountRef = useRef(0);
  const mountCountRef = useRef(0);

  // 模拟在 Strict Mode 下可能出现问题的组件
  const ProblematicPortal = ({ id, onError }) => {
    const [isMounted, setIsMounted] = useState(false);
    const elementRef = useRef(null);
    const renderCount = useRef(0);

    renderCount.current++;
    console.log(`Portal ${id} render #${renderCount.current}`);

    useEffect(() => {
      mountCountRef.current++;
      console.log(`Portal ${id} mounting (total mounts: ${mountCountRef.current})`);
      
      setIsMounted(true);

      // 模拟一些可能在 Strict Mode 下出问题的 DOM 操作
      const element = document.createElement('div');
      element.id = `portal-element-${id}`;
      element.style.cssText = `
        position: fixed;
        top: ${Math.random() * 200}px;
        left: ${Math.random() * 200}px;
        width: 120px;
        height: 60px;
        background: rgba(0, 255, 0, 0.7);
        border: 2px solid green;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        color: black;
      `;
      element.textContent = `Strict ${id}`;
      
      try {
        // 这里可能会在 Strict Mode 的双重渲染中出现问题
        document.body.appendChild(element);
        elementRef.current = element;
      } catch (error) {
        console.error(`Error appending element for Portal ${id}:`, error);
        onError && onError(error);
      }

      // 清理函数 - 在 Strict Mode 下会被调用两次
      return () => {
        console.log(`Portal ${id} cleanup`);
        if (elementRef.current && elementRef.current.parentNode) {
          try {
            elementRef.current.parentNode.removeChild(elementRef.current);
          } catch (error) {
            console.error(`Error removing element for Portal ${id}:`, error);
            onError && onError(error);
          }
        }
        setIsMounted(false);
      };
    }, [id, onError]);

    // 在 Strict Mode 下，这个 Portal 可能会在不合适的时机尝试渲染
    if (!isMounted) {
      return null;
    }

    return createPortal(
      <div style={{
        padding: '4px',
        background: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '4px',
        fontSize: '10px',
      }}>
        Portal {id}
        <br />
        Renders: {renderCount.current}
      </div>,
      elementRef.current || document.body
    );
  };

  // 启动严格模式测试
  const startStrictModeTest = () => {
    setIsRunning(true);
    renderCountRef.current = 0;
    mountCountRef.current = 0;
    
    console.log("🔄 Starting React Strict Mode DOM test...");
    console.log("This test will rapidly create/destroy components to trigger Strict Mode issues");

    // 快速创建多个组件来触发 Strict Mode 的双重渲染问题
    const createPortals = () => {
      const newPortals = [];
      for (let i = 0; i < 5; i++) {
        newPortals.push({
          id: `strict-${Date.now()}-${i}`,
          key: Math.random(),
        });
      }
      setPortals(newPortals);
    };

    // 快速变化状态来模拟实际应用中的复杂状态更新
    const stateChanges = [
      () => createPortals(),
      () => setPortals(prev => prev.slice(0, 3)), // 移除一些
      () => createPortals(), // 重新创建
      () => setPortals([]), // 清空
      () => createPortals(), // 再次创建
      () => setPortals(prev => [...prev, { id: `extra-${Date.now()}`, key: Math.random() }]), // 添加一个
      () => setPortals([]), // 最终清空
    ];

    stateChanges.forEach((change, index) => {
      setTimeout(change, index * 100);
    });

    // 测试结束
    setTimeout(() => {
      setIsRunning(false);
      console.log("🔄 Strict Mode test completed");
      console.log(`Total renders: ${renderCountRef.current}, Total mounts: ${mountCountRef.current}`);
    }, stateChanges.length * 100 + 200);
  };

  const handlePortalError = (error) => {
    console.error("🚨 Portal Error in Strict Mode Test:", error);
  };

  return (
    <div className="space-y-4 bg-green-50 border border-green-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-green-800">
        🔄 React Strict Mode DOM Test
      </h3>
      <p className="text-sm text-green-700">
        This test specifically targets issues that arise from React Strict Mode's double rendering.
        In development, React intentionally double-invokes effects and renders to help detect side effects.
        <strong> This can cause DOM manipulation timing issues!</strong>
      </p>
      
      <div className="flex gap-4 items-center">
        <Button
          onClick={startStrictModeTest}
          disabled={isRunning}
          variant="destructive"
          className="bg-green-600 hover:bg-green-700"
        >
          {isRunning ? "Running Strict Mode Test..." : "🔄 Start Strict Mode Test"}
        </Button>
        
        <div className="text-sm text-green-600">
          Active Portals: {portals.length}
        </div>
      </div>

      {isRunning && (
        <div className="bg-green-100 border border-green-300 rounded p-3">
          <p className="text-green-800 text-sm">
            🔄 Strict Mode test running... 
            <br />
            Watch for green boxes appearing/disappearing and check console for double-render logs!
            <br />
            <strong>Look for DOM manipulation errors in the console!</strong>
          </p>
        </div>
      )}

      <div className="text-xs text-green-600 bg-green-100 p-2 rounded">
        <strong>What to look for:</strong>
        <ul className="list-disc list-inside mt-1">
          <li>Console logs showing double renders/mounts</li>
          <li>DOM errors related to appendChild/removeChild</li>
          <li>insertBefore errors when DOM state is inconsistent</li>
          <li>Green boxes appearing and disappearing rapidly</li>
        </ul>
      </div>

      {/* 渲染所有测试 Portal */}
      {portals.map(portal => (
        <ProblematicPortal
          key={portal.key}
          id={portal.id}
          onError={handlePortalError}
        />
      ))}
    </div>
  );
};

export default StrictModeTest;
