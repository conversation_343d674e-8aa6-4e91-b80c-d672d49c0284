"use client";

import * as React from "react";
import { createPortal } from "react-dom";

// Portal 管理器，用于避免多个 Portal 同时操作 DOM 时的冲突
class PortalManager {
  constructor() {
    this.portals = new Map();
    this.portalContainer = null;
    this.isInitialized = false;
  }

  initialize() {
    if (this.isInitialized || typeof window === "undefined") {
      return;
    }

    // 创建一个专用的 Portal 容器
    this.portalContainer = document.createElement("div");
    this.portalContainer.id = "portal-root";
    this.portalContainer.style.position = "fixed";
    this.portalContainer.style.top = "0";
    this.portalContainer.style.left = "0";
    this.portalContainer.style.zIndex = "9999";
    this.portalContainer.style.pointerEvents = "none";
    
    document.body.appendChild(this.portalContainer);
    this.isInitialized = true;
  }

  createPortal(children, key) {
    if (!this.isInitialized) {
      this.initialize();
    }

    if (!this.portalContainer) {
      // 回退到默认行为
      return createPortal(children, document.body);
    }

    return createPortal(children, this.portalContainer);
  }

  cleanup() {
    if (this.portalContainer && this.portalContainer.parentNode) {
      this.portalContainer.parentNode.removeChild(this.portalContainer);
    }
    this.portals.clear();
    this.isInitialized = false;
  }
}

// 全局 Portal 管理器实例
const portalManager = new PortalManager();

// 安全的 Portal 组件
export const SafePortal = ({ children, container }) => {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
    portalManager.initialize();

    return () => {
      // 组件卸载时不清理全局管理器，因为其他组件可能还在使用
    };
  }, []);

  if (!mounted) {
    return null;
  }

  // 如果指定了容器，使用指定的容器，否则使用管理器
  if (container) {
    return createPortal(children, container);
  }

  return portalManager.createPortal(children);
};

// 改进的 Dialog Portal
export const SafeDialogPortal = ({ children, container }) => {
  return <SafePortal container={container}>{children}</SafePortal>;
};

// 导出管理器实例供其他地方使用
export { portalManager };
