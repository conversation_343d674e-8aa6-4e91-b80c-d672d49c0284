"use client";

import React from "react";
import * as Sentry from "@sentry/nextjs";

class DOMErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // 检查是否是 DOM 操作相关的错误
    const isDOMError = 
      error.name === "NotFoundError" ||
      error.message?.includes("insertBefore") ||
      error.message?.includes("removeChild") ||
      error.message?.includes("appendChild") ||
      error.message?.includes("Node");

    if (isDOMError) {
      console.warn("DOM Error caught by boundary:", error);
      return { hasError: true, error };
    }

    // 对于非 DOM 错误，让它继续向上传播
    throw error;
  }

  componentDidCatch(error, errorInfo) {
    // 只记录 DOM 相关错误，避免重复记录
    const isDOMError = 
      error.name === "NotFoundError" ||
      error.message?.includes("insertBefore") ||
      error.message?.includes("removeChild") ||
      error.message?.includes("appendChild") ||
      error.message?.includes("Node");

    if (isDOMError) {
      console.error("DOM Error Details:", {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
      });

      // 发送到 Sentry，但标记为低优先级
      Sentry.withScope((scope) => {
        scope.setTag("error_type", "dom_manipulation");
        scope.setLevel("warning"); // 设置为警告级别而不是错误
        scope.setContext("dom_error", {
          errorName: error.name,
          errorMessage: error.message,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        });
        Sentry.captureException(error);
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      // 对于 DOM 错误，提供一个简单的重试机制
      return (
        <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Temporary Display Issue
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  A temporary rendering issue occurred. This is usually harmless and resolves automatically.
                </p>
              </div>
              <div className="mt-4">
                <div className="-mx-2 -my-1.5 flex">
                  <button
                    type="button"
                    className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                    onClick={this.handleRetry}
                  >
                    Retry
                  </button>
                  {process.env.NODE_ENV === "development" && (
                    <button
                      type="button"
                      className="ml-3 bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                      onClick={() => console.log("DOM Error:", this.state.error)}
                    >
                      Log Details
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default DOMErrorBoundary;
