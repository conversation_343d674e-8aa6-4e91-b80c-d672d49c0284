"use client";

import { useEffect } from "react";
import NavBar from "@/components/Home/Navbar";
import Footer from "@/components/Home/Footer";
import { useAuthStore } from "@/stores/useAuthStore";
import UmamiAnalytics from "@/components/Analytics/UmamiAnalytics";
import ClarityAnalytics from "@/components/Analytics/ClarityAnalytics";
import GoogleAnalytics from "@/components/Analytics/GoogleAnalytics";
import PlausibleAnalytics from "@/components/Analytics/PlausibleAnalytics";
import ChristmasBanner from "@/components/Home/ChristmasBanner";
import MaintenanceDialog from "@/components/Common/MaintenanceDialog";
import IncidentDialog from "@/components/Common/IncidentDialog";
import { BannerProvider } from "@/contexts/BannerContext";
import { usePathname } from "@/i18n/navigation"; // 从我们的 navigation 文件导入
import DOMErrorBoundary from "@/components/ErrorBoundary/DOMErrorBoundary";
import domErrorHandler from "@/utils/domErrorHandler";

export default function ClientLayout({ children }) {
  const pathname = usePathname();
  const isToolsPath = pathname?.startsWith("/tools");

  useEffect(() => {
    useAuthStore.getState().initialize();

    // 初始化 DOM 错误处理器（仅开发环境）
    if (process.env.NODE_ENV === "development") {
      domErrorHandler.initialize();
    }
  }, []);

  return (
    <DOMErrorBoundary>
      <BannerProvider>
        <ChristmasBanner />
        <MaintenanceDialog />
        <IncidentDialog />
        <NavBar />
        <main className={`${isToolsPath ? "mt-20" : ""}`}>{children}</main>
        <Footer />
        <PlausibleAnalytics />
        <UmamiAnalytics />
        <ClarityAnalytics />
        <GoogleAnalytics />
      </BannerProvider>
    </DOMErrorBoundary>
  );
}
