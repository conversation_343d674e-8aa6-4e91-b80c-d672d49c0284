"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

export default function TestDOMPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [multipleDialogs, setMultipleDialogs] = useState([false, false, false]);

  const toggleMultipleDialog = (index) => {
    const newState = [...multipleDialogs];
    newState[index] = !newState[index];
    setMultipleDialogs(newState);
  };

  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">DOM Error Test Page</h1>
        <p className="text-gray-600 mb-8">
          This page tests various DOM operations that might trigger the insertBefore error.
        </p>
      </div>

      {/* Single Dialog Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Single Dialog Test</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>Open Single Dialog</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Test Dialog</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p>This is a test dialog to check DOM operations.</p>
              <Button 
                onClick={() => setDialogOpen(false)}
                className="mt-4"
              >
                Close Dialog
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Alert Dialog Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Alert Dialog Test</h2>
        <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
          <AlertDialogTrigger asChild>
            <Button variant="destructive">Open Alert Dialog</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This is a test alert dialog to check DOM operations.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Multiple Dialogs Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Multiple Dialogs Test</h2>
        <p className="text-sm text-gray-600">
          This tests the scenario that often causes Portal conflicts.
        </p>
        <div className="flex gap-4">
          {multipleDialogs.map((isOpen, index) => (
            <Dialog key={index} open={isOpen} onOpenChange={(open) => toggleMultipleDialog(index)}>
              <DialogTrigger asChild>
                <Button variant="outline">Dialog {index + 1}</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Dialog {index + 1}</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                  <p>This is dialog number {index + 1}.</p>
                  <Button 
                    onClick={() => toggleMultipleDialog(index)}
                    className="mt-4"
                  >
                    Close
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          ))}
        </div>
        <Button 
          onClick={() => setMultipleDialogs([true, true, true])}
          className="ml-4"
          variant="secondary"
        >
          Open All Dialogs
        </Button>
      </div>

      {/* Rapid Open/Close Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Rapid Open/Close Test</h2>
        <p className="text-sm text-gray-600">
          This tests rapid dialog operations that might cause timing issues.
        </p>
        <Button 
          onClick={() => {
            // Rapidly open and close dialogs
            for (let i = 0; i < 5; i++) {
              setTimeout(() => {
                setDialogOpen(true);
                setTimeout(() => setDialogOpen(false), 100);
              }, i * 200);
            }
          }}
          variant="secondary"
        >
          Rapid Open/Close Test
        </Button>
      </div>

      {/* Debug Info */}
      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold">Debug Information</h2>
        <div className="text-sm space-y-2">
          <p><strong>React Strict Mode:</strong> {process.env.NODE_ENV === 'development' ? 'Disabled (for testing)' : 'N/A'}</p>
          <p><strong>Portal Manager:</strong> Active</p>
          <p><strong>DOM Error Handler:</strong> {process.env.NODE_ENV === 'development' ? 'Active' : 'Disabled'}</p>
          <p><strong>Error Boundary:</strong> Active</p>
        </div>
        <Button 
          onClick={() => {
            console.log("Current DOM state:");
            console.log("Portals:", document.querySelectorAll('[data-radix-portal], [data-portal], #portal-root'));
            console.log("Dialogs:", document.querySelectorAll('[role="dialog"]'));
          }}
          variant="outline"
          size="sm"
        >
          Log DOM State
        </Button>
      </div>
    </div>
  );
}
