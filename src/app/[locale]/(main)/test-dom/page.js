"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import PortalStressTest from "@/components/test/PortalStressTest";
import RadixUIConflictTest from "@/components/test/RadixUIConflictTest";
import StrictModeTest from "@/components/test/StrictModeTest";

export default function TestDOMPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [multipleDialogs, setMultipleDialogs] = useState([false, false, false]);
  const [errorCount, setErrorCount] = useState(0);
  const [isStressTest, setIsStressTest] = useState(false);

  const toggleMultipleDialog = (index) => {
    const newState = [...multipleDialogs];
    newState[index] = !newState[index];
    setMultipleDialogs(newState);
  };

  // 监听全局错误来计数 DOM 错误
  useEffect(() => {
    const handleError = (event) => {
      const error = event.error;
      if (
        error &&
        (error.name === "NotFoundError" ||
          error.message?.includes("insertBefore") ||
          error.message?.includes("removeChild") ||
          error.message?.includes("appendChild") ||
          error.message?.includes("Node"))
      ) {
        console.error("🚨 DOM Error detected:", error);
        setErrorCount((prev) => prev + 1);
      }
    };

    window.addEventListener("error", handleError);
    return () => window.removeEventListener("error", handleError);
  }, []);

  // 压力测试：快速创建和销毁多个 Portal
  const runStressTest = async () => {
    setIsStressTest(true);
    console.log("🧪 Starting DOM stress test...");

    try {
      // 快速打开多个对话框
      for (let i = 0; i < 10; i++) {
        setTimeout(() => {
          setMultipleDialogs([true, true, true]);
          setTimeout(() => {
            setMultipleDialogs([false, false, false]);
          }, 50);
        }, i * 100);
      }

      // 同时进行单个对话框的快速开关
      for (let i = 0; i < 20; i++) {
        setTimeout(() => {
          setDialogOpen(true);
          setTimeout(() => setDialogOpen(false), 25);
        }, i * 50);
      }

      // 混合测试：对话框和警告框同时操作
      for (let i = 0; i < 15; i++) {
        setTimeout(() => {
          setAlertOpen(true);
          setDialogOpen(true);
          setTimeout(() => {
            setAlertOpen(false);
            setDialogOpen(false);
          }, 30);
        }, i * 80);
      }
    } finally {
      setTimeout(() => {
        setIsStressTest(false);
        console.log("🧪 Stress test completed");
      }, 3000);
    }
  };

  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">DOM Error Reproduction Test</h1>
        <p className="text-gray-600 mb-4">
          This page is designed to reproduce the insertBefore DOM error.
        </p>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <h2 className="text-lg font-semibold text-red-800 mb-2">
            Error Counter
          </h2>
          <p className="text-red-700">
            DOM Errors Detected:{" "}
            <span className="font-bold text-xl">{errorCount}</span>
          </p>
          <p className="text-sm text-red-600 mt-2">
            Check browser console for detailed error information
          </p>
        </div>
      </div>

      {/* Single Dialog Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Single Dialog Test</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>Open Single Dialog</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Test Dialog</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p>This is a test dialog to check DOM operations.</p>
              <Button onClick={() => setDialogOpen(false)} className="mt-4">
                Close Dialog
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Alert Dialog Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Alert Dialog Test</h2>
        <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
          <AlertDialogTrigger asChild>
            <Button variant="destructive">Open Alert Dialog</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This is a test alert dialog to check DOM operations.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      {/* Multiple Dialogs Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Multiple Dialogs Test</h2>
        <p className="text-sm text-gray-600">
          This tests the scenario that often causes Portal conflicts.
        </p>
        <div className="flex gap-4">
          {multipleDialogs.map((isOpen, index) => (
            <Dialog
              key={index}
              open={isOpen}
              onOpenChange={(open) => toggleMultipleDialog(index)}
            >
              <DialogTrigger asChild>
                <Button variant="outline">Dialog {index + 1}</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Dialog {index + 1}</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                  <p>This is dialog number {index + 1}.</p>
                  <Button
                    onClick={() => toggleMultipleDialog(index)}
                    className="mt-4"
                  >
                    Close
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          ))}
        </div>
        <Button
          onClick={() => setMultipleDialogs([true, true, true])}
          className="ml-4"
          variant="secondary"
        >
          Open All Dialogs
        </Button>
      </div>

      {/* React Strict Mode Test */}
      <StrictModeTest />

      {/* Real-World Radix UI Conflict Test */}
      <RadixUIConflictTest />

      {/* Aggressive Portal Stress Test */}
      <PortalStressTest />

      {/* DOM Error Stress Test */}
      <div className="space-y-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-xl font-semibold text-yellow-800">
          🧪 DOM Error Stress Test (Dialog Based)
        </h2>
        <p className="text-sm text-yellow-700">
          This test rapidly creates and destroys multiple Dialog Portal
          components to trigger DOM errors.
          <strong> This WILL likely cause insertBefore errors!</strong>
        </p>
        <div className="flex gap-4">
          <Button
            onClick={runStressTest}
            disabled={isStressTest}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700"
          >
            {isStressTest ? "Running Stress Test..." : "🚨 Run DOM Stress Test"}
          </Button>
          <Button onClick={() => setErrorCount(0)} variant="outline">
            Reset Error Counter
          </Button>
        </div>
        {isStressTest && (
          <div className="bg-yellow-100 border border-yellow-300 rounded p-3">
            <p className="text-yellow-800 text-sm">
              ⚠️ Stress test running... Watch the console for DOM errors!
            </p>
          </div>
        )}
      </div>

      {/* Rapid Open/Close Test */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Rapid Open/Close Test</h2>
        <p className="text-sm text-gray-600">
          This tests rapid dialog operations that might cause timing issues.
        </p>
        <Button
          onClick={() => {
            // Rapidly open and close dialogs
            for (let i = 0; i < 5; i++) {
              setTimeout(() => {
                setDialogOpen(true);
                setTimeout(() => setDialogOpen(false), 100);
              }, i * 200);
            }
          }}
          variant="secondary"
        >
          Rapid Open/Close Test
        </Button>
      </div>

      {/* Debug Info */}
      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold">Debug Information</h2>
        <div className="text-sm space-y-2">
          <p>
            <strong>React Strict Mode:</strong>{" "}
            {process.env.NODE_ENV === "development"
              ? "Enabled (Default - May Cause Errors)"
              : "N/A"}
          </p>
          <p>
            <strong>Portal Manager:</strong> Not Applied
          </p>
          <p>
            <strong>DOM Error Handler:</strong> Not Applied
          </p>
          <p>
            <strong>Error Boundary:</strong> Not Applied
          </p>
          <p>
            <strong>Current Environment:</strong> {process.env.NODE_ENV}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => {
              console.log("🔍 Current DOM state:");
              console.log(
                "Portals:",
                document.querySelectorAll(
                  "[data-radix-portal], [data-portal], #portal-root"
                )
              );
              console.log(
                "Dialogs:",
                document.querySelectorAll('[role="dialog"]')
              );
              console.log("Body children:", document.body.children.length);
              console.log("Body style:", {
                overflow: document.body.style.overflow,
                pointerEvents: document.body.style.pointerEvents,
              });
            }}
            variant="outline"
            size="sm"
          >
            Log DOM State
          </Button>
          <Button
            onClick={() => {
              console.clear();
              setErrorCount(0);
            }}
            variant="outline"
            size="sm"
          >
            Clear Console & Reset
          </Button>
        </div>
      </div>
    </div>
  );
}
