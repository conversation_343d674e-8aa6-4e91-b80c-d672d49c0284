// DOM 错误处理工具
// 用于捕获和分析 insertBefore 等 DOM 操作错误

class DOMErrorHandler {
  constructor() {
    this.errors = [];
    this.isInitialized = false;
    this.originalMethods = {};
  }

  initialize() {
    if (this.isInitialized || typeof window === "undefined") {
      return;
    }

    // 只在开发环境启用
    if (process.env.NODE_ENV !== "development") {
      return;
    }

    console.log("🔧 DOM Error Handler initialized");

    // 监听全局错误
    window.addEventListener("error", this.handleGlobalError.bind(this));
    window.addEventListener("unhandledrejection", this.handleUnhandledRejection.bind(this));

    // 包装 DOM 方法以捕获错误
    this.wrapDOMMethods();

    this.isInitialized = true;
  }

  handleGlobalError(event) {
    const error = event.error;
    if (this.isDOMError(error)) {
      console.warn("🚨 DOM Error detected:", {
        message: error.message,
        stack: error.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: new Date().toISOString(),
      });

      this.logDOMState();
      
      // 阻止错误继续传播到控制台（可选）
      // event.preventDefault();
    }
  }

  handleUnhandledRejection(event) {
    const error = event.reason;
    if (this.isDOMError(error)) {
      console.warn("🚨 DOM Promise Rejection:", {
        reason: error.message || error,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      this.logDOMState();
    }
  }

  isDOMError(error) {
    if (!error) return false;
    
    const errorMessage = error.message || String(error);
    const errorName = error.name || "";

    return (
      errorName === "NotFoundError" ||
      errorMessage.includes("insertBefore") ||
      errorMessage.includes("removeChild") ||
      errorMessage.includes("appendChild") ||
      errorMessage.includes("Node") ||
      errorMessage.includes("not a child of this node")
    );
  }

  wrapDOMMethods() {
    // 包装 Node.prototype.insertBefore
    if (Node.prototype.insertBefore) {
      this.originalMethods.insertBefore = Node.prototype.insertBefore;
      Node.prototype.insertBefore = function(newNode, referenceNode) {
        try {
          return domErrorHandler.originalMethods.insertBefore.call(this, newNode, referenceNode);
        } catch (error) {
          console.warn("🚨 insertBefore error caught:", {
            error: error.message,
            parentNode: this.nodeName,
            newNode: newNode?.nodeName,
            referenceNode: referenceNode?.nodeName,
            parentChildren: Array.from(this.children || []).map(c => c.nodeName),
          });
          
          // 尝试修复：如果 referenceNode 不是子节点，则 appendChild
          if (referenceNode && !this.contains(referenceNode)) {
            console.warn("🔧 Attempting to fix by using appendChild instead");
            return this.appendChild(newNode);
          }
          
          throw error;
        }
      };
    }

    // 包装 Node.prototype.removeChild
    if (Node.prototype.removeChild) {
      this.originalMethods.removeChild = Node.prototype.removeChild;
      Node.prototype.removeChild = function(child) {
        try {
          return domErrorHandler.originalMethods.removeChild.call(this, child);
        } catch (error) {
          console.warn("🚨 removeChild error caught:", {
            error: error.message,
            parentNode: this.nodeName,
            childNode: child?.nodeName,
            parentChildren: Array.from(this.children || []).map(c => c.nodeName),
          });
          
          // 如果子节点不存在，静默忽略
          if (!this.contains(child)) {
            console.warn("🔧 Child not found, ignoring removeChild");
            return child;
          }
          
          throw error;
        }
      };
    }
  }

  logDOMState() {
    console.group("🔍 DOM State Analysis");
    
    // 检查 Portal 容器
    const portals = document.querySelectorAll('[data-radix-portal], [data-portal], #portal-root');
    console.log("Portal containers:", portals.length);
    portals.forEach((portal, index) => {
      console.log(`Portal ${index}:`, {
        id: portal.id,
        className: portal.className,
        children: portal.children.length,
        parent: portal.parentNode?.nodeName,
      });
    });

    // 检查 Dialog 相关元素
    const dialogs = document.querySelectorAll('[role="dialog"], [data-state="open"]');
    console.log("Dialog elements:", dialogs.length);
    dialogs.forEach((dialog, index) => {
      console.log(`Dialog ${index}:`, {
        role: dialog.getAttribute("role"),
        state: dialog.getAttribute("data-state"),
        parent: dialog.parentNode?.nodeName,
      });
    });

    // 检查 body 的状态
    console.log("Body state:", {
      children: document.body.children.length,
      style: {
        overflow: document.body.style.overflow,
        pointerEvents: document.body.style.pointerEvents,
      },
      inert: document.body.hasAttribute("inert"),
    });

    console.groupEnd();
  }

  cleanup() {
    if (!this.isInitialized) return;

    // 恢复原始方法
    Object.keys(this.originalMethods).forEach(method => {
      if (Node.prototype[method]) {
        Node.prototype[method] = this.originalMethods[method];
      }
    });

    // 移除事件监听器
    window.removeEventListener("error", this.handleGlobalError.bind(this));
    window.removeEventListener("unhandledrejection", this.handleUnhandledRejection.bind(this));

    this.isInitialized = false;
    console.log("🔧 DOM Error Handler cleaned up");
  }
}

// 创建全局实例
const domErrorHandler = new DOMErrorHandler();

// 自动初始化
if (typeof window !== "undefined") {
  domErrorHandler.initialize();
}

export default domErrorHandler;
